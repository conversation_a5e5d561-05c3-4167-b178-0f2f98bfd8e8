<template>
    <!-- 地图容器 -->
    <div
      id="allmap"
      class="map-container"
    />
</template>

<script>
import useMap from './mixins/useMap'
export default {
  name: 'gisMap',
  mixins: [useMap],
  components: {

  },
  data() {
    return {
      // circuitName: this.$route.query.circuitName,
      map: null,
      configEditing: {
        mapCenter: [108.953418, 34.274803],
        mapZoom: 13
      },
    }
  },
  mounted() {
    // console.log("🚀 ~ mounted ~ this.circuitName:", this.circuitName)
    this.initMapFn()
  },
  methods: {

  },
}
</script>

<style scoped lang='less'>
#allmap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  font-family: "微软雅黑";
}
</style>

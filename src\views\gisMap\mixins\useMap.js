export default {
  methods: {
    initMapFn() {
      this.map = new BMapGL.Map('allmap');
      const center = new BMapGL.Point(
        this.configEditing.mapCenter[0],
        this.configEditing.mapCenter[1]
      );
      this.map.centerAndZoom(center, this.configEditing.mapZoom);
      this.map.enableScrollWheelZoom(true);
      this.map.setTilt(45);
      // 监听地图加载完成
      this.map.addEventListener('tilesloaded', () => {
        this.mapLoaded = true;
        this.addMapLayers()
      });
      var styleOptions = {
        strokeColor: '#5E87DB',   // 边线颜色
        fillColor: '#5E87DB',     // 填充颜色。当参数为空时，圆形没有填充颜色
        strokeWeight: 2,          // 边线宽度，以像素为单位
        strokeOpacity: 1,         // 边线透明度，取值范围0-1
        fillOpacity: 0.2          // 填充透明度，取值范围0-1
      };
      var labelOptions = {
        borderRadius: '2px',
        background: '#FFFBCC',
        border: '1px solid #E1E1E1',
        color: '#703A04',
        fontSize: '12px',
        letterSpacing: '0',
        padding: '5px'
      };
      // // 实例化鼠标绘制工具
      this.drawingManager = new BMapGLLib.DrawingManager(this.map, {
        enableCalculate: false, // 绘制是否进行测距测面
        enableSorption: true,   // 是否开启边界吸附功能
        sorptiondistance: 20,   // 边界吸附距离
        circleOptions: styleOptions,     // 圆的样式
        polylineOptions: styleOptions,   // 线的样式
        polygonOptions: styleOptions,    // 多边形的样式
        rectangleOptions: styleOptions,  // 矩形的样式
        labelOptions: labelOptions,      // label样式
      });
    },


    // 添加地图图层
    addMapLayers() {
      this.map.clearOverlays();
      // const point = new BMapGL.Point(108.9710, 34.2258);
      // // 创建自定义图标
      // const icon = new BMapGL.Icon(
      //   require('@/assets/images/icon_1.png'), // 图标路径
      //   new BMapGL.Size(32, 32), // 图标大小
      //   {
      //     anchor: new BMapGL.Size(16, 32), // 图标锚点
      //     imageOffset: new BMapGL.Size(0, 0) // 图标偏移
      //   }
      // );
      // const marker = new BMapGL.Marker(point
      //   // , { icon }
      // ); // 创建标注，并设置图标为自定义图标
      // this.map.addOverlay(marker);



      // 西安五个地标点的坐标和信息
      const landmarks = [
        { name: '小寨', lng: 108.9434, lat: 34.2178,description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房` },
        { name: '钟楼', lng: 108.9402, lat: 34.2583,description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房` },
        { name: '大雁塔', lng: 108.9646, lat: 34.2192,description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房` },
        { name: '西安万象城', lng: 108.9563, lat: 34.2314,description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房` },
        { name: '西安北站', lng: 108.9633, lat: 34.3708,description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房` }
      ];
      
      // 为每个地标创建标记点
      landmarks.forEach((landmark, index) => {
        const point = new BMapGL.Point(landmark.lng, landmark.lat);
        
        // 创建自定义图标
        const icon = new BMapGL.Icon(
          require('@/assets/images/icon_1.png'),
          new BMapGL.Size(32, 32),
          {
            anchor: new BMapGL.Size(16, 32),
            imageOffset: new BMapGL.Size(0, 0)
          }
        );
        
        const marker = new BMapGL.Marker(point
          // , { icon: icon }
        );
        
        // 添加标签
        const label = new BMapGL.Label(landmark.description, {
          offset: new BMapGL.Size(0, -40)
        });
        label.setStyle({
          color: '#fff',
          backgroundColor: 'rgba(0,0,0,0.7)',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px'
        });
        marker.setLabel(label);
        
        this.map.addOverlay(marker);
      });
    },

  }
}